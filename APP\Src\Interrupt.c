/**
 * @file Interrupt.c
 * <AUTHOR> name (<EMAIL>)
 * @brief 存储的是各种中断相关的
 * @version 0.1
 * @date 2025-07-12
 * 
 * @copyright Copyright (c) 2025
 * 
 */
#include "Interrupt.h"

uint8_t enable_group1_irq = 1;
bool Flag_Serial_RXcplt = false; //接收完成标志位
uint32_t ExISR_Flag; //中断判断标志位

#define ISR_IS_GPIO(X)        (ExISR_Flag & (X))
#define GET_RDR_B_VAL(X)      DL_GPIO_readPins(SPD_READER_B_PORT, (X))

/**
 * @brief Systick时钟中断
 * 
 */
void SysTick_Handler(void)
{
    SysTick_Increasment();
}



/**
 * @brief 外部中断
 * 
 */
void GROUP1_IRQHandler(void)
{
    //判断是不是由GPIOB触发的中断
    if (DL_Interrupt_getPendingGroup(DL_INTERRUPT_GROUP_1) == SPD_READER_A_INT_IIDX)
    {
        //查看哪个IO进入外部中断
        ExISR_Flag = SPD_READER_GET_ISR_STATUS(SPD_READER_A_FONT_LEFT_A_PIN | SPD_READER_A_FONT_RIGHT_A_PIN);

        if (ISR_IS_GPIO(SPD_READER_A_FONT_LEFT_A_PIN)) //左前轮
        {
            if (GET_RDR_B_VAL(SPD_READER_B_FONT_LEFT_B_PIN)) (*Motor_Font_Left.Motor_Encoder_Addr)++;
            else (*Motor_Font_Left.Motor_Encoder_Addr)--;
            SPD_READER_CLR_ISR_FLAG(SPD_READER_A_FONT_LEFT_A_PIN);
        }

        if (ISR_IS_GPIO(SPD_READER_A_FONT_RIGHT_A_PIN)) //右前轮
        {
            if (GET_RDR_B_VAL(SPD_READER_B_FONT_RIGHT_B_PIN)) (*Motor_Font_Right.Motor_Encoder_Addr)--;
            else (*Motor_Font_Right.Motor_Encoder_Addr)++;
            SPD_READER_CLR_ISR_FLAG(SPD_READER_A_FONT_RIGHT_A_PIN);
        }
    }
}


// 摄像头UART中断处理函数已移至Camera.c中实现
// 使用兼容性宏定义：UART_CAMERA_INST_IRQHandler -> UART_WIT_INST_IRQHandler
// 实际的中断处理逻辑请参考APP/Src/Camera.c中的实现



/**
 * @brief 中断初始化
 * 
 */
void Interrupt_Init(void)
{
    SPD_READER_CLR_ISR_FLAG(SPD_READER_A_FONT_LEFT_A_PIN);
    SPD_READER_CLR_ISR_FLAG(SPD_READER_A_FONT_RIGHT_A_PIN);
    
    DL_GPIO_enableInterrupt(SPD_READER_A_PORT, SPD_READER_A_FONT_LEFT_A_PIN);
    DL_GPIO_enableInterrupt(SPD_READER_A_PORT, SPD_READER_A_FONT_RIGHT_A_PIN);
    
    NVIC_EnableIRQ(SPD_READER_A_INT_IRQN);
}


