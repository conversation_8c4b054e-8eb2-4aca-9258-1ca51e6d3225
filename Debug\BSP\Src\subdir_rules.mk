################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
BSP/Src/%.o: ../BSP/Src/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/desk/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.4/BSP/Inc" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.4" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.4/DMP" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.4/App/Inc" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.4/Debug" -I"D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.4/APP/Inc" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -gdwarf-3 -w -MMD -MP -MF"BSP/Src/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

BSP/Src/PID.o: ../BSP/Src/PID.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/desk/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.4/BSP/Inc" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.4" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.4/DMP" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.4/App/Inc" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.4/Debug" -I"D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.4/APP/Inc" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -gdwarf-3 -w -MMD -MP -MF"BSP/Src/$(basename $(<F)).d_raw" -MT"BSP/Src/$(basename\ $(<F)).o"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


